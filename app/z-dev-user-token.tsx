import React, { useState, useEffect } from 'react';

import {
    View,
    Text,
    StyleSheet,
    Image,
    TouchableOpacity,
    ImageBackground,
    Animated,
    StatusBar,
    Platform
} from 'react-native';

import { router } from 'expo-router';

import { Storage } from 'expo-sqlite/kv-store';

import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { HugeiconsIcon } from '@hugeicons/react-native';
import { ArrowLeft01Icon } from '@hugeicons/core-free-icons';

import axiosApi from '../axios/axiosApi';



export default function ZDevUserTokenScreen() {
    const insets = useSafeAreaInsets()

    const [userToken, setUserToken] = useState('')
    const [expiresAt, setExpiresAt] = useState('')
    const [expiresIn, setExpiresIn] = useState('')

    useEffect(() => {
        console.log('xxxxxxxxxxxxxxxxxxxx')

        checkLoginStatus()

        checkTokenTime()
    }, [])

    const checkLoginStatus = async () => {
        const token = await Storage.getItem('userToken')
        console.log(token)

        if (token) {
            setUserToken(token)
        }
    };

    const checkTokenTime = async () => {
        try {
            const response: any = await axiosApi.post('/x_mooood_user', {
                do_sth: 'check_token',
            })

            console.log('Token check response:', response)

            if (response.code === 6666) {
                console.log('Token 有效', response?.data?.expiresAt, response?.data?.expiresIn)
                setExpiresAt(response?.data?.expiresAt || '')
                setExpiresIn(response?.data?.expiresIn || '')
            } else {
                console.log('Token 无效', response.msg)
                setExpiresAt('')
                setExpiresIn('')
            }
        } catch (error) {
            console.error('网络错误 Error checking token', error)
        }
    }

    const handleTopLeftBack = () => {
        router.back()
    }



    return (
        <>
            <StatusBar translucent backgroundColor="transparent" barStyle="dark-content" />
            <ImageBackground
                source={require('../assets/images/bg4.png')}
                style={styles.backgroundImage}
            >
                <View style={[styles.all, { paddingTop: insets.top }]}>
                    <View style={styles.head_nav}>
                        <TouchableOpacity onPress={handleTopLeftBack}>
                            <HugeiconsIcon
                                icon={ArrowLeft01Icon}
                                size={40}
                                color="black"
                                strokeWidth={1.2}
                            />
                        </TouchableOpacity>

                        <Text style={styles.head_nav_title}> user token </Text>

                        <HugeiconsIcon
                            icon={ArrowLeft01Icon}
                            size={40}
                            color="#00000000" // 设置为透明
                            strokeWidth={1.2}
                        />
                    </View>

                    <View style={styles.content}>
                        <Text style={styles.user_token_text}>
                            {
                                String(userToken)
                            }
                        </Text>

                        <Text style={styles.expires_label}>过期时间</Text>
                        <Text style={styles.expires_text}>
                            {expiresAt || '---------------'}
                        </Text>

                        <Text style={styles.expires_label}>剩余时间</Text>
                        <Text style={styles.expires_text}>
                            {expiresIn || '-----'}
                        </Text>
                    </View>
                </View>
            </ImageBackground>
        </>

    );
}

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,

        width: '100%',
        height: '100%',
    },

    all: {
        flex: 1,

        // justifyContent: 'center',
        // alignItems: 'center',
        // backgroundColor: '#f40',
    },

    head_nav: {
        height: 50,
        width: '100%',

        // paddingHorizontal: 5,

        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',

        // backgroundColor: '#f40',
    },
    head_nav_title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },


    content: {
        flex: 1,
        padding: 16,
        // backgroundColor: 'white',

    },

    






    user_token_text: {
        fontSize: 25,

        marginBottom: 80,
    },

    expires_label: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        marginTop: 20,
        marginBottom: 5,
    },

    expires_text: {
        fontSize: 16,
        color: '#666',
        marginBottom: 10,
    },
});
